# -*- coding: utf-8 -*-
"""
Módulo de Transcrição com API Gemini
Transcreve áudios e vídeos usando Google Gemini AI
"""

import google.generativeai as genai
import os
import tempfile
from pathlib import Path
import subprocess
import json
from datetime import datetime
import time

class GeminiTranscriber:
    def __init__(self, api_key):
        """
        Inicializa o transcriptor com a API key do Gemini
        """
        self.api_key = api_key
        genai.configure(api_key=api_key)
        
        # Configurar o modelo
        self.model = genai.GenerativeModel('gemini-1.5-pro')
        
        # Configurações de geração
        self.generation_config = {
            'temperature': 0.1,
            'top_p': 0.8,
            'top_k': 40,
            'max_output_tokens': 8192,
        }
        
    def extrair_audio_video(self, video_path, output_audio_path):
        """
        Extrai áudio de um arquivo de vídeo usando FFmpeg
        """
        try:
            cmd = [
                'ffmpeg', '-i', video_path,
                '-vn',  # Sem vídeo
                '-acodec', 'libmp3lame',  # Codec de áudio
                '-ar', '16000',  # Sample rate
                '-ac', '1',  # Mono
                '-b:a', '64k',  # Bitrate
                '-y',  # Sobrescrever arquivo existente
                output_audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            else:
                print(f"Erro FFmpeg: {result.stderr}")
                return False
                
        except FileNotFoundError:
            print("FFmpeg não encontrado. Instale o FFmpeg primeiro.")
            return False
        except Exception as e:
            print(f"Erro ao extrair áudio: {e}")
            return False
    
    def dividir_audio_chunks(self, audio_path, chunk_duration=60):
        """
        Divide o áudio em chunks menores para processamento
        """
        chunks = []
        temp_dir = tempfile.mkdtemp()
        
        try:
            # Obter duração do áudio
            cmd_duration = [
                'ffprobe', '-v', 'quiet', '-show_entries', 
                'format=duration', '-of', 'csv=p=0', audio_path
            ]
            
            result = subprocess.run(cmd_duration, capture_output=True, text=True)
            duration = float(result.stdout.strip())
            
            # Dividir em chunks
            num_chunks = int(duration / chunk_duration) + 1
            
            for i in range(num_chunks):
                start_time = i * chunk_duration
                chunk_path = os.path.join(temp_dir, f"chunk_{i:03d}.mp3")
                
                cmd_chunk = [
                    'ffmpeg', '-i', audio_path,
                    '-ss', str(start_time),
                    '-t', str(chunk_duration),
                    '-acodec', 'copy',
                    '-y', chunk_path
                ]
                
                result = subprocess.run(cmd_chunk, capture_output=True, text=True)
                
                if result.returncode == 0 and os.path.exists(chunk_path):
                    chunks.append(chunk_path)
                    
            return chunks
            
        except Exception as e:
            print(f"Erro ao dividir áudio: {e}")
            return []
    
    def transcrever_audio_chunk(self, audio_path):
        """
        Transcreve um chunk de áudio usando Gemini
        """
        try:
            # Upload do arquivo de áudio
            audio_file = genai.upload_file(audio_path)
            
            # Aguardar processamento
            while audio_file.state.name == "PROCESSING":
                time.sleep(2)
                audio_file = genai.get_file(audio_file.name)
            
            if audio_file.state.name == "FAILED":
                raise Exception("Falha no processamento do áudio")
            
            # Prompt para transcrição em português brasileiro
            prompt = """
            Transcreva este áudio para texto em português brasileiro.
            
            Instruções:
            1. Mantenha a linguagem natural e fluida
            2. Corrija erros gramaticais óbvios
            3. Use pontuação adequada
            4. Mantenha o contexto e significado original
            5. Se houver múltiplos falantes, indique quando possível
            6. Retorne apenas o texto transcrito, sem comentários adicionais
            
            Áudio para transcrever:
            """
            
            # Gerar transcrição
            response = self.model.generate_content(
                [prompt, audio_file],
                generation_config=self.generation_config
            )
            
            # Limpar arquivo temporário
            genai.delete_file(audio_file.name)
            
            return response.text.strip()
            
        except Exception as e:
            print(f"Erro na transcrição: {e}")
            return None
    
    def transcrever_arquivo(self, arquivo_path, callback_progresso=None):
        """
        Transcreve um arquivo completo (áudio ou vídeo)
        """
        arquivo_path = Path(arquivo_path)
        
        if callback_progresso:
            callback_progresso(f"Iniciando transcrição de {arquivo_path.name}")
        
        # Verificar se é vídeo ou áudio
        extensoes_video = {'.mp4', '.avi', '.mov', '.mkv', '.webm'}
        extensoes_audio = {'.mp3', '.wav', '.m4a', '.flac', '.aac'}
        
        if arquivo_path.suffix.lower() in extensoes_video:
            # Extrair áudio do vídeo
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_audio:
                temp_audio_path = temp_audio.name
            
            if callback_progresso:
                callback_progresso("Extraindo áudio do vídeo...")
            
            if not self.extrair_audio_video(str(arquivo_path), temp_audio_path):
                return None
                
            audio_path = temp_audio_path
            
        elif arquivo_path.suffix.lower() in extensoes_audio:
            audio_path = str(arquivo_path)
        else:
            raise ValueError(f"Formato de arquivo não suportado: {arquivo_path.suffix}")
        
        try:
            # Dividir em chunks
            if callback_progresso:
                callback_progresso("Dividindo áudio em segmentos...")
            
            chunks = self.dividir_audio_chunks(audio_path)
            
            if not chunks:
                # Se não conseguir dividir, tentar transcrever o arquivo inteiro
                chunks = [audio_path]
            
            # Transcrever cada chunk
            transcricoes = []
            total_chunks = len(chunks)
            
            for i, chunk_path in enumerate(chunks):
                if callback_progresso:
                    callback_progresso(f"Transcrevendo segmento {i+1}/{total_chunks}...")
                
                transcricao = self.transcrever_audio_chunk(chunk_path)
                
                if transcricao:
                    transcricoes.append(transcricao)
                
                # Limpar chunk temporário
                if chunk_path != audio_path:
                    try:
                        os.unlink(chunk_path)
                    except:
                        pass
            
            # Limpar arquivo de áudio temporário se foi extraído de vídeo
            if arquivo_path.suffix.lower() in extensoes_video:
                try:
                    os.unlink(audio_path)
                except:
                    pass
            
            # Juntar todas as transcrições
            texto_completo = " ".join(transcricoes)
            
            if callback_progresso:
                callback_progresso("Transcrição concluída!")
            
            return {
                'texto': texto_completo,
                'arquivo_original': str(arquivo_path),
                'data_transcricao': datetime.now().isoformat(),
                'chunks_processados': len(transcricoes),
                'palavras_estimadas': len(texto_completo.split())
            }
            
        except Exception as e:
            if callback_progresso:
                callback_progresso(f"Erro na transcrição: {e}")
            return None
    
    def melhorar_texto_com_gemini(self, texto, callback_progresso=None):
        """
        Melhora o texto transcrito usando Gemini para correção e formatação
        """
        try:
            if callback_progresso:
                callback_progresso("Melhorando texto com IA...")
            
            prompt = f"""
            Melhore este texto transcrito seguindo as diretrizes abaixo:
            
            1. Corrija erros gramaticais e ortográficos
            2. Melhore a pontuação e estrutura das frases
            3. Organize em parágrafos coerentes
            4. Mantenha o significado e contexto original
            5. Use linguagem formal e acadêmica quando apropriado
            6. Remova repetições desnecessárias
            7. Mantenha a linguagem em português brasileiro
            
            Texto para melhorar:
            
            {texto}
            
            Retorne apenas o texto melhorado, sem comentários adicionais.
            """
            
            response = self.model.generate_content(
                prompt,
                generation_config=self.generation_config
            )
            
            return response.text.strip()
            
        except Exception as e:
            print(f"Erro ao melhorar texto: {e}")
            return texto  # Retorna o texto original se houver erro
    
    def gerar_resumo(self, texto, callback_progresso=None):
        """
        Gera um resumo do texto transcrito
        """
        try:
            if callback_progresso:
                callback_progresso("Gerando resumo...")
            
            prompt = f"""
            Crie um resumo executivo deste texto transcrito:
            
            1. Identifique os pontos principais
            2. Mantenha a estrutura lógica
            3. Use linguagem clara e objetiva
            4. Limite a 300-500 palavras
            5. Organize em tópicos quando apropriado
            
            Texto para resumir:
            
            {texto}
            
            Resumo:
            """
            
            response = self.model.generate_content(
                prompt,
                generation_config=self.generation_config
            )
            
            return response.text.strip()
            
        except Exception as e:
            print(f"Erro ao gerar resumo: {e}")
            return None
    
    def extrair_palavras_chave(self, texto, callback_progresso=None):
        """
        Extrai palavras-chave do texto transcrito
        """
        try:
            if callback_progresso:
                callback_progresso("Extraindo palavras-chave...")
            
            prompt = f"""
            Extraia as palavras-chave mais importantes deste texto:
            
            1. Identifique termos técnicos relevantes
            2. Encontre conceitos principais
            3. Liste nomes próprios importantes
            4. Retorne 10-20 palavras-chave
            5. Ordene por relevância
            6. Separe por vírgulas
            
            Texto para análise:
            
            {texto}
            
            Palavras-chave:
            """
            
            response = self.model.generate_content(
                prompt,
                generation_config=self.generation_config
            )
            
            palavras = [p.strip() for p in response.text.split(',')]
            return palavras[:20]  # Limitar a 20 palavras-chave
            
        except Exception as e:
            print(f"Erro ao extrair palavras-chave: {e}")
            return []

def testar_gemini_transcriber():
    """
    Função de teste para o GeminiTranscriber
    """
    # Substitua pela sua API key
    api_key = "SUA_API_KEY_AQUI"
    
    transcriber = GeminiTranscriber(api_key)
    
    # Teste com um arquivo de exemplo
    arquivo_teste = "exemplo.mp3"  # Substitua pelo seu arquivo
    
    def callback_teste(mensagem):
        print(f"Status: {mensagem}")
    
    resultado = transcriber.transcrever_arquivo(arquivo_teste, callback_teste)
    
    if resultado:
        print("Transcrição bem-sucedida!")
        print(f"Texto: {resultado['texto'][:200]}...")
        print(f"Palavras: {resultado['palavras_estimadas']}")
    else:
        print("Falha na transcrição")

if __name__ == "__main__":
    testar_gemini_transcriber()
