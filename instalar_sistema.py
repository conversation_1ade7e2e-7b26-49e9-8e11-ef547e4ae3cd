# -*- coding: utf-8 -*-
"""
Script de Instalação Automática
Sistema de Transcrição Gemini ABNT
"""

import subprocess
import sys
import os
from pathlib import Path

def print_banner():
    """Exibe banner do sistema"""
    print("=" * 70)
    print("🎯 INSTALADOR DO SISTEMA DE TRANSCRIÇÃO GEMINI ABNT")
    print("=" * 70)
    print("Sistema profissional de transcrição com formatação ABNT")
    print("Desenvolvido para processar vídeos e áudios em lote")
    print("=" * 70)
    print()

def verificar_python():
    """Verifica se a versão do Python é adequada"""
    print("🔍 Verificando versão do Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Erro: Python 3.7 ou superior é necessário")
        print(f"   Versão atual: {version.major}.{version.minor}.{version.micro}")
        print("   Baixe a versão mais recente em: https://python.org")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
    return True

def instalar_dependencias():
    """Instala as dependências necessárias"""
    print("\n📦 Instalando dependências...")
    
    dependencias = [
        "google-generativeai>=0.3.0",
        "python-docx>=0.8.11", 
        "reportlab>=3.6.0",
        "moviepy>=1.0.3",
        "pydub>=0.25.1"
    ]
    
    for dep in dependencias:
        print(f"   Instalando {dep}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"   ✅ {dep} - Instalado")
        except subprocess.CalledProcessError:
            print(f"   ❌ Erro ao instalar {dep}")
            return False
    
    print("✅ Todas as dependências instaladas com sucesso!")
    return True

def verificar_ffmpeg():
    """Verifica se FFmpeg está instalado"""
    print("\n🎬 Verificando FFmpeg...")
    
    try:
        subprocess.run(["ffmpeg", "-version"], 
                      capture_output=True, check=True)
        print("✅ FFmpeg encontrado - OK")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  FFmpeg não encontrado")
        print("   Para processar vídeos, instale o FFmpeg:")
        print("   1. Baixe em: https://ffmpeg.org/download.html")
        print("   2. Extraia e adicione ao PATH do sistema")
        print("   3. Reinicie o terminal")
        print("   (Opcional: você pode usar apenas áudios)")
        return False

def criar_arquivo_config():
    """Cria arquivo de configuração inicial"""
    print("\n⚙️ Criando configuração inicial...")
    
    config_content = """{
  "api_key": "",
  "autor": "",
  "instituicao": "",
  "pasta_saida": "resultados",
  "formatos": {
    "txt": true,
    "markdown": true,
    "pdf": false,
    "docx": false
  }
}"""
    
    try:
        with open("config_gemini.json", "w", encoding="utf-8") as f:
            f.write(config_content)
        print("✅ Arquivo de configuração criado")
        return True
    except Exception as e:
        print(f"❌ Erro ao criar configuração: {e}")
        return False

def criar_pasta_resultados():
    """Cria pasta para resultados"""
    print("\n📁 Criando pasta de resultados...")
    
    try:
        Path("resultados").mkdir(exist_ok=True)
        print("✅ Pasta 'resultados' criada")
        return True
    except Exception as e:
        print(f"❌ Erro ao criar pasta: {e}")
        return False

def verificar_arquivos_sistema():
    """Verifica se todos os arquivos do sistema estão presentes"""
    print("\n📋 Verificando arquivos do sistema...")
    
    arquivos_necessarios = [
        "transcricao_gemini_gui.py",
        "gemini_transcriber.py", 
        "abnt_formatter.py"
    ]
    
    todos_presentes = True
    for arquivo in arquivos_necessarios:
        if Path(arquivo).exists():
            print(f"   ✅ {arquivo}")
        else:
            print(f"   ❌ {arquivo} - AUSENTE")
            todos_presentes = False
    
    return todos_presentes

def mostrar_instrucoes_finais():
    """Mostra instruções finais de uso"""
    print("\n" + "=" * 70)
    print("🎉 INSTALAÇÃO CONCLUÍDA COM SUCESSO!")
    print("=" * 70)
    print()
    print("📋 PRÓXIMOS PASSOS:")
    print()
    print("1️⃣ OBTER API KEY DO GEMINI (GRATUITA):")
    print("   • Acesse: https://makersuite.google.com/app/apikey")
    print("   • Faça login com conta Google")
    print("   • Clique 'Create API Key'")
    print("   • Copie a chave gerada")
    print()
    print("2️⃣ EXECUTAR O SISTEMA:")
    print("   python transcricao_gemini_gui.py")
    print()
    print("3️⃣ CONFIGURAR NA INTERFACE:")
    print("   • Cole a API Key na aba 'Configuração'")
    print("   • Configure autor e instituição")
    print("   • Escolha formatos de exportação")
    print()
    print("4️⃣ USAR O SISTEMA:")
    print("   • Aba 'Arquivos': Adicione vídeos/áudios")
    print("   • Aba 'Processamento': Inicie transcrição")
    print("   • Aba 'Resultados': Veja arquivos gerados")
    print()
    print("📚 DOCUMENTAÇÃO:")
    print("   • GUIA_RAPIDO.md - Guia de uso rápido")
    print("   • INSTRUCOES_DE_USO.md - Manual completo")
    print()
    print("🎯 FORMATOS SUPORTADOS:")
    print("   • Vídeos: MP4, AVI, MOV, MKV")
    print("   • Áudios: MP3, WAV, M4A, FLAC, AAC")
    print()
    print("📤 EXPORTAÇÃO:")
    print("   • TXT (ABNT), Markdown, PDF, DOCX")
    print()
    print("=" * 70)
    print("Sistema pronto para transcrever com qualidade profissional!")
    print("=" * 70)

def main():
    """Função principal do instalador"""
    print_banner()
    
    # Verificações e instalações
    if not verificar_python():
        return False
    
    if not instalar_dependencias():
        print("\n❌ Falha na instalação das dependências")
        return False
    
    verificar_ffmpeg()  # Não é crítico
    
    if not verificar_arquivos_sistema():
        print("\n❌ Arquivos do sistema ausentes")
        print("   Certifique-se de ter todos os arquivos na pasta")
        return False
    
    if not criar_arquivo_config():
        return False
    
    if not criar_pasta_resultados():
        return False
    
    mostrar_instrucoes_finais()
    return True

if __name__ == "__main__":
    try:
        sucesso = main()
        if sucesso:
            input("\nPressione ENTER para continuar...")
        else:
            print("\n❌ Instalação falhou. Verifique os erros acima.")
            input("Pressione ENTER para sair...")
    except KeyboardInterrupt:
        print("\n\n❌ Instalação cancelada pelo usuário.")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        input("Pressione ENTER para sair...")
