# Sistema de Transcrição de Vídeo para Texto ABNT

Sistema Python para transcrever vídeos em texto seguindo as normas ABNT brasileiras.

## 🚀 Funcionalidades

- ✅ Transcrição automática de vídeos MP4 para texto
- ✅ Formatação ABNT completa (parágrafos, espaçamento, cabeçalhos)
- ✅ Divisão inteligente em chunks para melhor precisão
- ✅ Interface interativa amigável
- ✅ Suporte a múltiplos vídeos em lote
- ✅ Configurações personalizáveis
- ✅ Limpeza e formatação automática do texto

## 📋 Pré-requisitos

### Software necessário:
- Python 3.7 ou superior
- FFmpeg (será configurado automaticamente)

### Dependências Python:
```bash
pip install -r requirements.txt
```

## 🛠️ Instalação

1. **Clone ou baixe os arquivos do projeto**
2. **Instale as dependências:**
   ```bash
   pip install moviepy SpeechRecognition pydub python-docx
   ```
3. **Execute o sistema:**
   ```bash
   python transcribe.py
   ```

## 📖 Como Usar

### Interface Interativa

Execute o arquivo principal:
```bash
python transcribe.py
```

O sistema apresentará um menu com as seguintes opções:

1. **Transcrever vídeo específico** - Escolha um vídeo da lista
2. **Transcrever todos os vídeos da pasta** - Processa todos os MP4
3. **Configurar FFmpeg** - Instala e configura o FFmpeg
4. **Configurar informações do documento** - Define autor, instituição, etc.
5. **Sair** - Encerra o programa

### Uso Direto (Programático)

```python
from transcribe import transcribe_video

# Transcrever um vídeo
success = transcribe_video(
    video_path="meu_video.mp4",
    output_path="transcricao.txt",
    title="Título do Documento",
    author="Seu Nome",
    institution="Sua Instituição"
)
```

## 📁 Estrutura dos Arquivos

```
projeto/
├── transcribe.py          # Arquivo principal
├── config.py             # Configurações do sistema
├── requirements.txt      # Dependências Python
├── README.md            # Este arquivo
└── seus_videos.mp4      # Vídeos para transcrever
```

## ⚙️ Configuração

### FFmpeg
O sistema configura automaticamente o FFmpeg. Se necessário, você pode:
1. Baixar o FFmpeg do site oficial
2. Usar a opção 3 do menu para configurar
3. Adicionar manualmente ao PATH do sistema

### Personalização
Edite o arquivo `config.py` para personalizar:
- Duração dos chunks de áudio
- Configurações ABNT
- Formatos de data
- Mensagens do sistema

## 📄 Formato de Saída ABNT

O texto transcrito segue as normas ABNT:
- ✅ Cabeçalho formatado
- ✅ Parágrafos com indentação (1,25cm simulado)
- ✅ Espaçamento adequado entre parágrafos
- ✅ Capitalização automática
- ✅ Correção de pontuação
- ✅ Metadados do documento
- ✅ Rodapé com informações

### Exemplo de saída:
```
================================================================================
TRANSCRIÇÃO - REUNIÃO GERAL
================================================================================

Autor: João Silva
Instituição: Universidade Federal
Data: 25 de março de 2025

--------------------------------------------------------------------------------

TRANSCRIÇÃO:

    Este é o primeiro parágrafo da transcrição, formatado seguindo as 
    normas ABNT com indentação adequada e espaçamento correto.

    Segundo parágrafo com continuação do texto transcrito, mantendo 
    a formatação e estrutura adequadas.

--------------------------------------------------------------------------------
Documento gerado automaticamente em 25 de março de 2025
Total de parágrafos: 2
================================================================================
```

## 🔧 Solução de Problemas

### Erro: "FFmpeg não encontrado"
1. Use a opção 3 do menu para configurar
2. Ou baixe manualmente do site oficial do FFmpeg
3. Adicione ao PATH do sistema

### Erro: "pydub não está instalado"
```bash
pip install pydub
```

### Erro: "SpeechRecognition não conseguiu entender o áudio"
- Verifique a qualidade do áudio
- Certifique-se de que há fala no vídeo
- Tente com um vídeo de melhor qualidade

### Erro de conexão com API do Google
- Verifique sua conexão com a internet
- O serviço é gratuito mas tem limites de uso

## 📊 Limitações

- Depende da API gratuita do Google Speech Recognition
- Funciona melhor com áudio claro e sem ruído
- Requer conexão com internet
- Limitado a arquivos MP4

## 🤝 Contribuições

Sinta-se à vontade para:
- Reportar bugs
- Sugerir melhorias
- Contribuir com código
- Melhorar a documentação

## 📝 Licença

Este projeto é de uso livre para fins educacionais e acadêmicos.

## 📞 Suporte

Para dúvidas ou problemas:
1. Verifique este README
2. Consulte os comentários no código
3. Teste com vídeos de boa qualidade primeiro
