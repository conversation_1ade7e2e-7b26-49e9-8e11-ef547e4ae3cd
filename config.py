# -*- coding: utf-8 -*-
"""
Configurações para o sistema de transcrição ABNT
"""

# Configurações de transcrição
TRANSCRIPTION_CONFIG = {
    "language": "pt-BR",
    "chunk_duration": 30,  # segundos
    "audio_format": "wav",
    "temp_audio_file": "temp_audio.wav"
}

# Configurações ABNT
ABNT_CONFIG = {
    "font_family": "Times New Roman",
    "font_size": 12,
    "line_spacing": 1.5,
    "paragraph_indent": "1.25cm",
    "margin_top": "3cm",
    "margin_bottom": "2cm",
    "margin_left": "3cm",
    "margin_right": "2cm",
    "page_numbering": True
}

# Configurações de documento padrão
DEFAULT_DOCUMENT_CONFIG = {
    "title": "TRANSCRIÇÃO DE ÁUDIO",
    "author": "",
    "institution": "",
    "subject": "Transcrição automática de áudio",
    "keywords": ["transcrição", "áudio", "ABNT"],
    "include_timestamp": True,
    "include_metadata": True
}

# Configurações de processamento de texto
TEXT_PROCESSING_CONFIG = {
    "max_words_per_paragraph": 80,
    "auto_capitalize": True,
    "fix_punctuation": True,
    "remove_extra_spaces": True,
    "split_long_sentences": True,
    "max_sentence_length": 150  # caracteres
}

# Configurações de saída
OUTPUT_CONFIG = {
    "encoding": "utf-8",
    "file_extension": ".txt",
    "backup_original": True,
    "create_summary": True,
    "include_statistics": True
}

# Configurações de FFmpeg
FFMPEG_CONFIG = {
    "archive_path": "C:\\Users\\<USER>\\Downloads\\ffmpeg-7.1.1.tar.xz",
    "extract_path": "C:\\ffmpeg",
    "bin_path": "C:\\ffmpeg\\ffmpeg-7.1.1\\bin"
}

# Mensagens do sistema
MESSAGES = {
    "start_transcription": "Iniciando transcrição de: {filename}",
    "transcription_complete": "Transcrição completa!",
    "transcription_saved": "Transcrição salva em: {filepath}",
    "error_processing": "Erro ao processar o vídeo: {error}",
    "error_audio_extraction": "Erro ao extrair áudio: {error}",
    "error_transcription": "Erro na transcrição: {error}",
    "chunk_transcribed": "Chunk {current}/{total} transcrito",
    "audio_inaudible": "[ÁUDIO INAUDÍVEL]",
    "transcription_error": "[ERRO NA TRANSCRIÇÃO]",
    "api_error": "Erro na API: {error}",
    "pydub_not_installed": "pydub não está instalado. Usando método simples...",
    "ffmpeg_extract_success": "FFmpeg extraído com sucesso",
    "ffmpeg_extract_error": "Erro ao extrair FFmpeg: {error}",
    "add_to_path": "Por favor, adicione o diretório '{path}' ao PATH do sistema.",
    "restart_terminal": "Após adicionar o diretório ao PATH, reinicie o terminal."
}

# Formatos de data
DATE_FORMATS = {
    "document": "%d de %B de %Y",
    "filename": "%Y%m%d_%H%M%S",
    "timestamp": "%d/%m/%Y %H:%M:%S"
}

# Configurações de qualidade de áudio
AUDIO_QUALITY_CONFIG = {
    "sample_rate": 16000,
    "channels": 1,  # mono
    "bit_depth": 16,
    "normalize_audio": True,
    "noise_reduction": False  # Requer bibliotecas adicionais
}
