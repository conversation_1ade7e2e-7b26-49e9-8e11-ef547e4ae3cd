# -*- coding: utf-8 -*-
"""
Módulo de Formatação ABNT
Formata textos transcritos seguindo normas ABNT brasileiras
Exporta em múltiplos formatos: TXT, Markdown, PDF, DOCX
"""

import re
from datetime import datetime
from pathlib import Path
import textwrap

class ABNTFormatter:
    def __init__(self):
        """
        Inicializa o formatador ABNT
        """
        self.config_abnt = {
            'fonte': 'Times New Roman',
            'tamanho_fonte': 12,
            'espacamento_linha': 1.5,
            'margem_superior': '3cm',
            'margem_inferior': '2cm',
            'margem_esquerda': '3cm',
            'margem_direita': '2cm',
            'recuo_paragrafo': '1.25cm',
            'espacamento_paragrafo': '6pt'
        }
        
        # Meses em português para formatação de data
        self.meses = {
            1: 'janeiro', 2: 'fevereiro', 3: 'março', 4: 'abril',
            5: 'maio', 6: 'junho', 7: 'julho', 8: 'agosto',
            9: 'setembro', 10: 'outubro', 11: 'novembro', 12: 'dezembro'
        }
    
    def formatar_data_abnt(self, data_iso=None):
        """
        Formata data no padrão ABNT (ex: 28 de maio de 2025)
        """
        if data_iso:
            try:
                data = datetime.fromisoformat(data_iso.replace('Z', '+00:00'))
            except:
                data = datetime.now()
        else:
            data = datetime.now()
        
        dia = data.day
        mes = self.meses[data.month]
        ano = data.year
        
        return f"{dia} de {mes} de {ano}"
    
    def limpar_e_formatar_texto(self, texto):
        """
        Limpa e formata o texto seguindo padrões ABNT
        """
        # Remover espaços extras
        texto = re.sub(r'\s+', ' ', texto.strip())
        
        # Corrigir pontuação
        texto = re.sub(r'\s+([,.!?;:])', r'\1', texto)
        texto = re.sub(r'([,.!?;:])\s*', r'\1 ', texto)
        
        # Capitalizar início de frases
        frases = re.split(r'([.!?]+)', texto)
        texto_formatado = []
        
        for i, frase in enumerate(frases):
            if i % 2 == 0 and frase.strip():  # Texto da frase
                frase = frase.strip()
                if frase:
                    frase = frase[0].upper() + frase[1:]
                    texto_formatado.append(frase)
            elif frase.strip():  # Pontuação
                texto_formatado.append(frase)
        
        return ''.join(texto_formatado)
    
    def dividir_em_paragrafos(self, texto, max_palavras=80):
        """
        Divide o texto em parágrafos seguindo padrões ABNT
        """
        palavras = texto.split()
        paragrafos = []
        paragrafo_atual = []
        
        for palavra in palavras:
            paragrafo_atual.append(palavra)
            
            # Criar novo parágrafo se atingir limite ou encontrar pausa natural
            if (len(paragrafo_atual) >= max_palavras and 
                palavra.endswith(('.', '!', '?')) or
                len(paragrafo_atual) >= max_palavras * 1.5):
                
                paragrafos.append(' '.join(paragrafo_atual))
                paragrafo_atual = []
        
        # Adicionar último parágrafo se houver
        if paragrafo_atual:
            paragrafos.append(' '.join(paragrafo_atual))
        
        return paragrafos
    
    def criar_cabecalho_abnt(self, metadados):
        """
        Cria cabeçalho formatado seguindo ABNT
        """
        titulo = metadados.get('titulo', 'TRANSCRIÇÃO DE ÁUDIO').upper()
        autor = metadados.get('autor', '')
        instituicao = metadados.get('instituicao', '')
        data = self.formatar_data_abnt(metadados.get('data_transcricao'))
        
        cabecalho = []
        cabecalho.append("=" * 80)
        cabecalho.append(titulo.center(80))
        cabecalho.append("=" * 80)
        cabecalho.append("")
        
        if autor:
            cabecalho.append(f"Autor: {autor}")
        if instituicao:
            cabecalho.append(f"Instituição: {instituicao}")
        
        cabecalho.append(f"Data: {data}")
        
        if metadados.get('arquivo_original'):
            cabecalho.append(f"Arquivo original: {metadados['arquivo_original']}")
        
        cabecalho.append("")
        cabecalho.append("-" * 80)
        
        return cabecalho
    
    def criar_rodape_abnt(self, metadados, num_paragrafos):
        """
        Cria rodapé formatado seguindo ABNT
        """
        data = self.formatar_data_abnt(metadados.get('data_transcricao'))
        
        rodape = []
        rodape.append("-" * 80)
        rodape.append("")
        rodape.append("INFORMAÇÕES DO DOCUMENTO:")
        rodape.append(f"• Documento gerado automaticamente em {data}")
        rodape.append(f"• Total de parágrafos: {num_paragrafos}")
        
        if metadados.get('palavras_estimadas'):
            rodape.append(f"• Palavras estimadas: {metadados['palavras_estimadas']}")
        
        if metadados.get('chunks_processados'):
            rodape.append(f"• Segmentos processados: {metadados['chunks_processados']}")
        
        rodape.append("")
        rodape.append("=" * 80)
        
        return rodape
    
    def exportar_txt(self, texto, metadados, arquivo_saida):
        """
        Exporta texto formatado em ABNT para arquivo TXT
        """
        try:
            # Limpar e formatar texto
            texto_limpo = self.limpar_e_formatar_texto(texto)
            paragrafos = self.dividir_em_paragrafos(texto_limpo)
            
            # Criar documento
            documento = []
            
            # Cabeçalho
            documento.extend(self.criar_cabecalho_abnt(metadados))
            documento.append("")
            
            # Corpo do documento
            documento.append("TRANSCRIÇÃO:")
            documento.append("")
            
            for paragrafo in paragrafos:
                # Indentação de parágrafo (1,25cm simulado com 4 espaços)
                paragrafo_indentado = textwrap.fill(
                    paragrafo, 
                    width=76, 
                    initial_indent="    ",
                    subsequent_indent="    "
                )
                documento.append(paragrafo_indentado)
                documento.append("")  # Espaçamento entre parágrafos
            
            # Resumo se disponível
            if metadados.get('resumo'):
                documento.append("")
                documento.append("RESUMO EXECUTIVO:")
                documento.append("")
                resumo_formatado = textwrap.fill(
                    metadados['resumo'],
                    width=76,
                    initial_indent="    ",
                    subsequent_indent="    "
                )
                documento.append(resumo_formatado)
                documento.append("")
            
            # Palavras-chave se disponíveis
            if metadados.get('palavras_chave'):
                documento.append("")
                documento.append("PALAVRAS-CHAVE:")
                documento.append("")
                palavras = ", ".join(metadados['palavras_chave'])
                palavras_formatadas = textwrap.fill(
                    palavras,
                    width=76,
                    initial_indent="    ",
                    subsequent_indent="    "
                )
                documento.append(palavras_formatadas)
                documento.append("")
            
            # Rodapé
            documento.extend(self.criar_rodape_abnt(metadados, len(paragrafos)))
            
            # Salvar arquivo
            with open(arquivo_saida, 'w', encoding='utf-8') as f:
                f.write('\n'.join(documento))
            
            return True
            
        except Exception as e:
            print(f"Erro ao exportar TXT: {e}")
            return False
    
    def exportar_markdown(self, texto, metadados, arquivo_saida):
        """
        Exporta texto formatado para Markdown
        """
        try:
            # Limpar e formatar texto
            texto_limpo = self.limpar_e_formatar_texto(texto)
            paragrafos = self.dividir_em_paragrafos(texto_limpo)
            
            # Criar documento Markdown
            md_content = []
            
            # Título principal
            titulo = metadados.get('titulo', 'TRANSCRIÇÃO DE ÁUDIO')
            md_content.append(f"# {titulo}")
            md_content.append("")
            
            # Metadados
            md_content.append("## Informações do Documento")
            md_content.append("")
            
            if metadados.get('autor'):
                md_content.append(f"**Autor:** {metadados['autor']}")
            if metadados.get('instituicao'):
                md_content.append(f"**Instituição:** {metadados['instituicao']}")
            
            data = self.formatar_data_abnt(metadados.get('data_transcricao'))
            md_content.append(f"**Data:** {data}")
            
            if metadados.get('arquivo_original'):
                md_content.append(f"**Arquivo original:** {metadados['arquivo_original']}")
            
            md_content.append("")
            md_content.append("---")
            md_content.append("")
            
            # Resumo se disponível
            if metadados.get('resumo'):
                md_content.append("## Resumo Executivo")
                md_content.append("")
                md_content.append(metadados['resumo'])
                md_content.append("")
            
            # Palavras-chave se disponíveis
            if metadados.get('palavras_chave'):
                md_content.append("## Palavras-chave")
                md_content.append("")
                palavras = ", ".join([f"`{p}`" for p in metadados['palavras_chave']])
                md_content.append(palavras)
                md_content.append("")
            
            # Transcrição
            md_content.append("## Transcrição")
            md_content.append("")
            
            for paragrafo in paragrafos:
                md_content.append(paragrafo)
                md_content.append("")
            
            # Informações técnicas
            md_content.append("---")
            md_content.append("")
            md_content.append("## Informações Técnicas")
            md_content.append("")
            md_content.append(f"- **Parágrafos:** {len(paragrafos)}")
            
            if metadados.get('palavras_estimadas'):
                md_content.append(f"- **Palavras estimadas:** {metadados['palavras_estimadas']}")
            
            if metadados.get('chunks_processados'):
                md_content.append(f"- **Segmentos processados:** {metadados['chunks_processados']}")
            
            md_content.append(f"- **Gerado em:** {data}")
            
            # Salvar arquivo
            with open(arquivo_saida, 'w', encoding='utf-8') as f:
                f.write('\n'.join(md_content))
            
            return True
            
        except Exception as e:
            print(f"Erro ao exportar Markdown: {e}")
            return False
    
    def exportar_pdf(self, texto, metadados, arquivo_saida):
        """
        Exporta texto formatado para PDF com formatação ABNT
        """
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import cm
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
            from reportlab.lib.enums import TA_JUSTIFY, TA_CENTER
            
            # Configurar documento
            doc = SimpleDocTemplate(
                arquivo_saida,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=3*cm,
                topMargin=3*cm,
                bottomMargin=2*cm
            )
            
            # Estilos
            styles = getSampleStyleSheet()
            
            # Estilo para título
            titulo_style = ParagraphStyle(
                'TituloABNT',
                parent=styles['Heading1'],
                fontSize=14,
                spaceAfter=30,
                alignment=TA_CENTER,
                fontName='Times-Bold'
            )
            
            # Estilo para texto normal
            texto_style = ParagraphStyle(
                'TextoABNT',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=6,
                spaceBefore=6,
                alignment=TA_JUSTIFY,
                fontName='Times-Roman',
                firstLineIndent=1.25*cm,
                leading=18  # Espaçamento 1.5
            )
            
            # Estilo para metadados
            meta_style = ParagraphStyle(
                'MetaABNT',
                parent=styles['Normal'],
                fontSize=11,
                spaceAfter=3,
                fontName='Times-Roman'
            )
            
            # Construir documento
            story = []
            
            # Título
            titulo = metadados.get('titulo', 'TRANSCRIÇÃO DE ÁUDIO')
            story.append(Paragraph(titulo.upper(), titulo_style))
            story.append(Spacer(1, 20))
            
            # Metadados
            if metadados.get('autor'):
                story.append(Paragraph(f"<b>Autor:</b> {metadados['autor']}", meta_style))
            if metadados.get('instituicao'):
                story.append(Paragraph(f"<b>Instituição:</b> {metadados['instituicao']}", meta_style))
            
            data = self.formatar_data_abnt(metadados.get('data_transcricao'))
            story.append(Paragraph(f"<b>Data:</b> {data}", meta_style))
            
            if metadados.get('arquivo_original'):
                story.append(Paragraph(f"<b>Arquivo original:</b> {metadados['arquivo_original']}", meta_style))
            
            story.append(Spacer(1, 30))
            
            # Resumo se disponível
            if metadados.get('resumo'):
                story.append(Paragraph("<b>RESUMO EXECUTIVO</b>", styles['Heading2']))
                story.append(Spacer(1, 12))
                story.append(Paragraph(metadados['resumo'], texto_style))
                story.append(Spacer(1, 20))
            
            # Palavras-chave se disponíveis
            if metadados.get('palavras_chave'):
                story.append(Paragraph("<b>PALAVRAS-CHAVE</b>", styles['Heading2']))
                story.append(Spacer(1, 12))
                palavras = ", ".join(metadados['palavras_chave'])
                story.append(Paragraph(palavras, texto_style))
                story.append(Spacer(1, 20))
            
            # Transcrição
            story.append(Paragraph("<b>TRANSCRIÇÃO</b>", styles['Heading2']))
            story.append(Spacer(1, 12))
            
            # Limpar e dividir texto
            texto_limpo = self.limpar_e_formatar_texto(texto)
            paragrafos = self.dividir_em_paragrafos(texto_limpo)
            
            for paragrafo in paragrafos:
                story.append(Paragraph(paragrafo, texto_style))
            
            # Gerar PDF
            doc.build(story)
            return True
            
        except ImportError:
            print("Erro: reportlab não está instalado. Use: pip install reportlab")
            return False
        except Exception as e:
            print(f"Erro ao exportar PDF: {e}")
            return False
    
    def exportar_docx(self, texto, metadados, arquivo_saida):
        """
        Exporta texto formatado para DOCX com formatação ABNT
        """
        try:
            from docx import Document
            from docx.shared import Inches, Pt
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.enum.style import WD_STYLE_TYPE
            
            # Criar documento
            doc = Document()
            
            # Configurar margens (ABNT)
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(1.18)    # 3cm
                section.bottom_margin = Inches(0.79) # 2cm
                section.left_margin = Inches(1.18)   # 3cm
                section.right_margin = Inches(0.79)  # 2cm
            
            # Configurar estilos
            styles = doc.styles
            
            # Estilo para título
            if 'TituloABNT' not in [s.name for s in styles]:
                titulo_style = styles.add_style('TituloABNT', WD_STYLE_TYPE.PARAGRAPH)
                titulo_style.font.name = 'Times New Roman'
                titulo_style.font.size = Pt(14)
                titulo_style.font.bold = True
                titulo_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
                titulo_style.paragraph_format.space_after = Pt(18)
            
            # Estilo para texto normal
            if 'TextoABNT' not in [s.name for s in styles]:
                texto_style = styles.add_style('TextoABNT', WD_STYLE_TYPE.PARAGRAPH)
                texto_style.font.name = 'Times New Roman'
                texto_style.font.size = Pt(12)
                texto_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
                texto_style.paragraph_format.first_line_indent = Inches(0.49)  # 1.25cm
                texto_style.paragraph_format.line_spacing = 1.5
                texto_style.paragraph_format.space_after = Pt(6)
            
            # Título
            titulo = metadados.get('titulo', 'TRANSCRIÇÃO DE ÁUDIO')
            titulo_para = doc.add_paragraph(titulo.upper(), style='TituloABNT')
            
            # Metadados
            doc.add_paragraph()  # Espaço
            
            if metadados.get('autor'):
                doc.add_paragraph(f"Autor: {metadados['autor']}")
            if metadados.get('instituicao'):
                doc.add_paragraph(f"Instituição: {metadados['instituicao']}")
            
            data = self.formatar_data_abnt(metadados.get('data_transcricao'))
            doc.add_paragraph(f"Data: {data}")
            
            if metadados.get('arquivo_original'):
                doc.add_paragraph(f"Arquivo original: {metadados['arquivo_original']}")
            
            doc.add_paragraph()  # Espaço
            
            # Resumo se disponível
            if metadados.get('resumo'):
                resumo_titulo = doc.add_paragraph("RESUMO EXECUTIVO")
                resumo_titulo.runs[0].bold = True
                doc.add_paragraph(metadados['resumo'], style='TextoABNT')
                doc.add_paragraph()  # Espaço
            
            # Palavras-chave se disponíveis
            if metadados.get('palavras_chave'):
                palavras_titulo = doc.add_paragraph("PALAVRAS-CHAVE")
                palavras_titulo.runs[0].bold = True
                palavras = ", ".join(metadados['palavras_chave'])
                doc.add_paragraph(palavras, style='TextoABNT')
                doc.add_paragraph()  # Espaço
            
            # Transcrição
            transcricao_titulo = doc.add_paragraph("TRANSCRIÇÃO")
            transcricao_titulo.runs[0].bold = True
            
            # Limpar e dividir texto
            texto_limpo = self.limpar_e_formatar_texto(texto)
            paragrafos = self.dividir_em_paragrafos(texto_limpo)
            
            for paragrafo in paragrafos:
                doc.add_paragraph(paragrafo, style='TextoABNT')
            
            # Salvar documento
            doc.save(arquivo_saida)
            return True
            
        except ImportError:
            print("Erro: python-docx não está instalado. Use: pip install python-docx")
            return False
        except Exception as e:
            print(f"Erro ao exportar DOCX: {e}")
            return False

def testar_formatter():
    """
    Função de teste para o ABNTFormatter
    """
    formatter = ABNTFormatter()
    
    # Texto de exemplo
    texto_exemplo = """
    Este é um exemplo de texto transcrito que precisa ser formatado seguindo as normas ABNT.
    O texto pode conter várias frases e parágrafos que devem ser organizados adequadamente.
    A formatação deve incluir indentação correta, espaçamento adequado e estrutura profissional.
    """
    
    # Metadados de exemplo
    metadados = {
        'titulo': 'EXEMPLO DE TRANSCRIÇÃO ABNT',
        'autor': 'João Silva',
        'instituicao': 'Universidade Federal do Brasil',
        'arquivo_original': 'exemplo.mp4',
        'data_transcricao': datetime.now().isoformat(),
        'palavras_estimadas': 50,
        'chunks_processados': 1,
        'resumo': 'Este é um resumo do exemplo de transcrição.',
        'palavras_chave': ['exemplo', 'transcrição', 'ABNT', 'formatação']
    }
    
    # Testar exportações
    print("Testando exportação TXT...")
    formatter.exportar_txt(texto_exemplo, metadados, 'teste_abnt.txt')
    
    print("Testando exportação Markdown...")
    formatter.exportar_markdown(texto_exemplo, metadados, 'teste_abnt.md')
    
    print("Testando exportação PDF...")
    formatter.exportar_pdf(texto_exemplo, metadados, 'teste_abnt.pdf')
    
    print("Testando exportação DOCX...")
    formatter.exportar_docx(texto_exemplo, metadados, 'teste_abnt.docx')
    
    print("Testes concluídos!")

if __name__ == "__main__":
    testar_formatter()
