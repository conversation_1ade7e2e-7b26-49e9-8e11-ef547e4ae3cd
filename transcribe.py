import speech_recognition as sr
import os
import tarfile
from moviepy.editor import VideoFileClip
from datetime import datetime
import re
import math

def extract_ffmpeg(archive_path, extract_path):
    """
    Extrai o arquivo ffmpeg.
    """
    try:
        with tarfile.open(archive_path, "r:xz") as tar:
            tar.extractall(extract_path)
        print(f"Arquivo {archive_path} extraído para {extract_path}")
    except Exception as e:
        print(f"Erro ao extrair o arquivo: {e}")
        return False
    return True

def transcribe_video(video_path, output_path, title="", author="", institution=""):
    """
    Transcreve um arquivo de vídeo com melhor qualidade e formatação ABNT.
    """
    try:
        print(f"Iniciando transcrição de: {video_path}")

        # 1. Extrair o áudio do vídeo usando moviepy
        video = VideoFileClip(video_path)
        audio_path = "temp_audio.wav"
        video.audio.write_audiofile(audio_path, verbose=False, logger=None)
        video.close()  # Liberar recursos

        # 2. Transcrever o áudio em chunks para melhor qualidade
        full_text = transcribe_audio_chunks(audio_path)

        if full_text:
            print("Transcrição completa!")

            # 3. Gerar título automático se não fornecido
            if not title:
                title = f"TRANSCRIÇÃO - {os.path.basename(video_path).replace('.mp4', '')}"

            # 4. Formatar o texto em ABNT
            formatted_text = format_abnt(full_text, title, author, institution)

            # 5. Salvar o texto formatado em um arquivo
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(formatted_text)

            print(f"Transcrição salva em: {output_path}")
            return True
        else:
            print("Não foi possível transcrever o áudio")
            return False

    except Exception as e:
        print(f"Erro ao processar o vídeo: {e}")
        return False
    finally:
        # Remover o arquivo de áudio temporário
        if os.path.exists("temp_audio.wav"):
            os.remove("temp_audio.wav")

def transcribe_audio_chunks(audio_path, chunk_duration=30):
    """
    Transcreve áudio dividindo em chunks para melhor precisão.
    """
    try:
        from pydub import AudioSegment

        # Carregar áudio
        audio = AudioSegment.from_wav(audio_path)

        # Dividir em chunks
        chunk_length_ms = chunk_duration * 1000
        chunks = []

        for i in range(0, len(audio), chunk_length_ms):
            chunk = audio[i:i + chunk_length_ms]
            chunks.append(chunk)

        print(f"Áudio dividido em {len(chunks)} chunks de {chunk_duration}s")

        # Transcrever cada chunk
        r = sr.Recognizer()
        full_transcription = []

        for i, chunk in enumerate(chunks):
            try:
                # Salvar chunk temporário
                chunk_path = f"temp_chunk_{i}.wav"
                chunk.export(chunk_path, format="wav")

                # Transcrever chunk
                with sr.AudioFile(chunk_path) as source:
                    audio_data = r.record(source)
                    text = r.recognize_google(audio_data, language="pt-BR")
                    full_transcription.append(text)
                    print(f"Chunk {i+1}/{len(chunks)} transcrito")

                # Remover chunk temporário
                os.remove(chunk_path)

            except sr.UnknownValueError:
                print(f"Chunk {i+1} não pôde ser transcrito (áudio inaudível)")
                full_transcription.append("[ÁUDIO INAUDÍVEL]")
            except sr.RequestError as e:
                print(f"Erro na API para chunk {i+1}: {e}")
                full_transcription.append("[ERRO NA TRANSCRIÇÃO]")
            except Exception as e:
                print(f"Erro no chunk {i+1}: {e}")
                full_transcription.append("[ERRO]")

        return " ".join(full_transcription)

    except ImportError:
        print("pydub não está instalado. Usando método simples...")
        return transcribe_audio_simple(audio_path)
    except Exception as e:
        print(f"Erro na transcrição por chunks: {e}")
        return transcribe_audio_simple(audio_path)

def transcribe_audio_simple(audio_path):
    """
    Método simples de transcrição (fallback).
    """
    try:
        r = sr.Recognizer()
        with sr.AudioFile(audio_path) as source:
            audio = r.record(source)
            text = r.recognize_google(audio, language="pt-BR")
            return text
    except sr.UnknownValueError:
        print("SpeechRecognition não conseguiu entender o áudio")
        return None
    except sr.RequestError as e:
        print(f"Erro na API: {e}")
        return None

def format_abnt(text, title="TRANSCRIÇÃO DE ÁUDIO", author="", institution=""):
    """
    Aplica formatação ABNT completa ao texto transcrito.
    """
    # Data atual para o documento
    current_date = datetime.now().strftime("%d de %B de %Y")

    # Limpar e formatar o texto
    cleaned_text = clean_transcription_text(text)

    # Dividir em parágrafos
    paragraphs = create_paragraphs(cleaned_text)

    # Criar documento formatado
    formatted_doc = create_abnt_document(title, author, institution, current_date, paragraphs)

    return formatted_doc

def clean_transcription_text(text):
    """
    Limpa e melhora o texto transcrito.
    """
    # Remover espaços extras
    text = re.sub(r'\s+', ' ', text)

    # Corrigir pontuação básica
    text = re.sub(r'\s+([,.!?;:])', r'\1', text)
    text = re.sub(r'([,.!?;:])\s*', r'\1 ', text)

    # Capitalizar início de frases
    sentences = re.split(r'([.!?]+)', text)
    cleaned_sentences = []

    for i, sentence in enumerate(sentences):
        if i % 2 == 0 and sentence.strip():  # Texto da frase
            sentence = sentence.strip()
            if sentence:
                sentence = sentence[0].upper() + sentence[1:].lower()
                cleaned_sentences.append(sentence)
        elif sentence.strip():  # Pontuação
            cleaned_sentences.append(sentence)

    return ''.join(cleaned_sentences)

def create_paragraphs(text, max_words_per_paragraph=80):
    """
    Divide o texto em parágrafos seguindo padrões ABNT.
    """
    words = text.split()
    paragraphs = []
    current_paragraph = []

    for word in words:
        current_paragraph.append(word)

        # Criar novo parágrafo se atingir limite ou encontrar pausa natural
        if (len(current_paragraph) >= max_words_per_paragraph and
            word.endswith(('.', '!', '?')) or
            len(current_paragraph) >= max_words_per_paragraph * 1.5):

            paragraphs.append(' '.join(current_paragraph))
            current_paragraph = []

    # Adicionar último parágrafo se houver
    if current_paragraph:
        paragraphs.append(' '.join(current_paragraph))

    return paragraphs

def create_abnt_document(title, author, institution, date, paragraphs):
    """
    Cria documento com formatação ABNT completa.
    """
    doc = []

    # Cabeçalho ABNT
    doc.append("=" * 80)
    doc.append(f"{title.upper()}")
    doc.append("=" * 80)
    doc.append("")

    if author:
        doc.append(f"Autor: {author}")
    if institution:
        doc.append(f"Instituição: {institution}")

    doc.append(f"Data: {date}")
    doc.append("")
    doc.append("-" * 80)
    doc.append("")

    # Corpo do documento
    doc.append("TRANSCRIÇÃO:")
    doc.append("")

    for i, paragraph in enumerate(paragraphs, 1):
        # Indentação de parágrafo (1,25 cm simulado com espaços)
        indented_paragraph = f"    {paragraph}"
        doc.append(indented_paragraph)
        doc.append("")  # Espaçamento entre parágrafos

    # Rodapé
    doc.append("-" * 80)
    doc.append(f"Documento gerado automaticamente em {date}")
    doc.append(f"Total de parágrafos: {len(paragraphs)}")
    doc.append("=" * 80)

    return '\n'.join(doc)

def interactive_menu():
    """
    Menu interativo para o sistema de transcrição.
    """
    try:
        from config import FFMPEG_CONFIG, MESSAGES, DEFAULT_DOCUMENT_CONFIG
    except ImportError:
        # Fallback se config.py não existir
        FFMPEG_CONFIG = {
            "archive_path": "C:\\Users\\<USER>\\Downloads\\ffmpeg-7.1.1.tar.xz",
            "extract_path": "C:\\ffmpeg",
            "bin_path": "C:\\ffmpeg\\ffmpeg-7.1.1\\bin"
        }
        MESSAGES = {
            "add_to_path": "Por favor, adicione o diretório '{path}' ao PATH do sistema.",
            "restart_terminal": "Após adicionar o diretório ao PATH, reinicie o terminal."
        }
        DEFAULT_DOCUMENT_CONFIG = {"title": "", "author": "", "institution": ""}

    print("=" * 60)
    print("    SISTEMA DE TRANSCRIÇÃO DE VÍDEO PARA TEXTO ABNT")
    print("=" * 60)
    print()

    while True:
        print("\nOpções disponíveis:")
        print("1. Transcrever vídeo específico")
        print("2. Transcrever todos os vídeos da pasta")
        print("3. Configurar FFmpeg")
        print("4. Configurar informações do documento")
        print("5. Sair")
        print()

        choice = input("Escolha uma opção (1-5): ").strip()

        if choice == "1":
            transcribe_single_video()
        elif choice == "2":
            transcribe_all_videos()
        elif choice == "3":
            setup_ffmpeg()
        elif choice == "4":
            configure_document()
        elif choice == "5":
            print("Saindo do sistema...")
            break
        else:
            print("Opção inválida! Tente novamente.")

def transcribe_single_video():
    """
    Transcreve um único vídeo escolhido pelo usuário.
    """
    print("\n--- TRANSCRIÇÃO DE VÍDEO ÚNICO ---")

    # Listar vídeos disponíveis
    video_files = [f for f in os.listdir('.') if f.endswith('.mp4')]

    if not video_files:
        print("Nenhum arquivo de vídeo (.mp4) encontrado na pasta atual.")
        return

    print("\nVídeos disponíveis:")
    for i, video in enumerate(video_files, 1):
        print(f"{i}. {video}")

    try:
        choice = int(input(f"\nEscolha um vídeo (1-{len(video_files)}): ")) - 1
        if 0 <= choice < len(video_files):
            video_path = video_files[choice]

            # Configurar saída
            output_path = input(f"Nome do arquivo de saída (padrão: {video_path.replace('.mp4', '_abnt.txt')}): ").strip()
            if not output_path:
                output_path = video_path.replace('.mp4', '_abnt.txt')

            # Configurar metadados
            title = input("Título do documento (opcional): ").strip()
            author = input("Autor (opcional): ").strip()
            institution = input("Instituição (opcional): ").strip()

            print(f"\nIniciando transcrição de: {video_path}")
            success = transcribe_video(video_path, output_path, title, author, institution)

            if success:
                print(f"✓ Transcrição concluída com sucesso!")
                print(f"  Arquivo salvo: {output_path}")
            else:
                print("✗ Erro na transcrição.")
        else:
            print("Escolha inválida!")
    except ValueError:
        print("Por favor, digite um número válido.")

def transcribe_all_videos():
    """
    Transcreve todos os vídeos da pasta atual.
    """
    print("\n--- TRANSCRIÇÃO DE TODOS OS VÍDEOS ---")

    video_files = [f for f in os.listdir('.') if f.endswith('.mp4')]

    if not video_files:
        print("Nenhum arquivo de vídeo (.mp4) encontrado na pasta atual.")
        return

    print(f"Encontrados {len(video_files)} vídeos para transcrever:")
    for video in video_files:
        print(f"  - {video}")

    confirm = input("\nDeseja continuar? (s/n): ").strip().lower()
    if confirm not in ['s', 'sim', 'y', 'yes']:
        print("Operação cancelada.")
        return

    # Configurar metadados globais
    print("\nConfiguração global (aplicada a todos os vídeos):")
    author = input("Autor (opcional): ").strip()
    institution = input("Instituição (opcional): ").strip()

    successful = 0
    failed = 0

    for video in video_files:
        output_file = video.replace(".mp4", "_abnt.txt")
        title = f"TRANSCRIÇÃO - {video.replace('.mp4', '')}"

        print(f"\nProcessando: {video}")
        success = transcribe_video(video, output_file, title, author, institution)

        if success:
            successful += 1
            print(f"✓ {video} - Concluído")
        else:
            failed += 1
            print(f"✗ {video} - Falhou")

    print(f"\n--- RESUMO ---")
    print(f"Sucessos: {successful}")
    print(f"Falhas: {failed}")
    print(f"Total: {len(video_files)}")

def setup_ffmpeg():
    """
    Configura o FFmpeg no sistema.
    """
    print("\n--- CONFIGURAÇÃO DO FFMPEG ---")

    try:
        from config import FFMPEG_CONFIG, MESSAGES
        archive_path = FFMPEG_CONFIG["archive_path"]
        extract_path = FFMPEG_CONFIG["extract_path"]
        bin_path = FFMPEG_CONFIG["bin_path"]
    except ImportError:
        archive_path = "C:\\Users\\<USER>\\Downloads\\ffmpeg-7.1.1.tar.xz"
        extract_path = "C:\\ffmpeg"
        bin_path = "C:\\ffmpeg\\ffmpeg-7.1.1\\bin"

    print(f"Arquivo FFmpeg: {archive_path}")
    print(f"Pasta de extração: {extract_path}")

    if not os.path.exists(archive_path):
        print(f"✗ Arquivo FFmpeg não encontrado em: {archive_path}")
        new_path = input("Digite o caminho correto do arquivo FFmpeg: ").strip()
        if os.path.exists(new_path):
            archive_path = new_path
        else:
            print("Arquivo não encontrado. Operação cancelada.")
            return

    print("Extraindo FFmpeg...")
    if extract_ffmpeg(archive_path, extract_path):
        print("✓ FFmpeg extraído com sucesso!")
        print(f"Por favor, adicione o diretório '{bin_path}' ao PATH do sistema.")
        print("Após adicionar ao PATH, reinicie o terminal.")
    else:
        print("✗ Falha na extração do FFmpeg.")

def configure_document():
    """
    Configura informações padrão do documento.
    """
    print("\n--- CONFIGURAÇÃO DO DOCUMENTO ---")
    print("Configure as informações padrão que serão usadas nos documentos:")
    print("(Deixe em branco para manter o valor atual)")

    try:
        from config import DEFAULT_DOCUMENT_CONFIG
        current_config = DEFAULT_DOCUMENT_CONFIG.copy()
    except ImportError:
        current_config = {"title": "", "author": "", "institution": ""}

    print(f"\nTítulo atual: {current_config.get('title', 'Não definido')}")
    new_title = input("Novo título: ").strip()
    if new_title:
        current_config['title'] = new_title

    print(f"Autor atual: {current_config.get('author', 'Não definido')}")
    new_author = input("Novo autor: ").strip()
    if new_author:
        current_config['author'] = new_author

    print(f"Instituição atual: {current_config.get('institution', 'Não definido')}")
    new_institution = input("Nova instituição: ").strip()
    if new_institution:
        current_config['institution'] = new_institution

    print("\n✓ Configurações atualizadas!")
    print("Nota: Para salvar permanentemente, edite o arquivo config.py")

if __name__ == "__main__":
    try:
        interactive_menu()
    except KeyboardInterrupt:
        print("\n\nOperação interrompida pelo usuário.")
    except Exception as e:
        print(f"\nErro inesperado: {e}")
        print("Por favor, verifique os arquivos e tente novamente.")