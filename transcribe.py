import speech_recognition as sr
import os
import tarfile
from moviepy.editor import VideoFileClip

def extract_ffmpeg(archive_path, extract_path):
    """
    Extrai o arquivo ffmpeg.
    """
    try:
        with tarfile.open(archive_path, "r:xz") as tar:
            tar.extractall(extract_path)
        print(f"Arquivo {archive_path} extraído para {extract_path}")
    except Exception as e:
        print(f"Erro ao extrair o arquivo: {e}")
        return False
    return True

def transcribe_video(video_path, output_path):
    """
    Transcreve um arquivo de vídeo, extrai o áudio,
    transcreve o áudio e salva o resultado em um arquivo de texto.
    """
    try:
        # 1. Extrair o áudio do vídeo usando moviepy
        video = VideoFileClip(video_path)
        audio_path = "temp_audio.wav"
        video.audio.write_audiofile(audio_path)

        # 2. Transcrever o áudio usando SpeechRecognition
        r = sr.Recognizer()
        with sr.AudioFile(audio_path) as source:
            audio = r.record(source)  # Lê todo o arquivo de áudio.

        try:
            text = r.recognize_google(audio, language="pt-BR")
            print("Transcrição completa: " + text)

            # 3. Formatar o texto em ABNT (simples)
            formatted_text = format_abnt(text)

            # 4. Salvar o texto formatado em um arquivo
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(formatted_text)

            print(f"Transcrição salva em {output_path}")

        except sr.UnknownValueError:
            print("SpeechRecognition não conseguiu entender o áudio")
        except sr.RequestError as e:
            print(f"Não foi possível solicitar resultados do serviço SpeechRecognition; {e}")

        finally:
            # Remover o arquivo de áudio temporário
            os.remove(audio_path)

    except Exception as e:
        print(f"Erro ao processar o vídeo: {e}")

def format_abnt(text):
    """
    Aplica uma formatação ABNT básica ao texto.
    """
    # Implementar formatação ABNT aqui (ex: espaçamento, margens, etc.)
    # Por enquanto, apenas converte para maiúsculas.
    return text.upper()

if __name__ == "__main__":
    # 1. Extrair o ffmpeg
    archive_path = "C:\\Users\\<USER>\\Downloads\\ffmpeg-7.1.1.tar.xz"
    extract_path = "C:\\ffmpeg"
    if extract_ffmpeg(archive_path, extract_path):
        # 2. Adicionar o diretório bin do ffmpeg ao PATH
        # (Não posso fazer isso automaticamente)
        print("Por favor, adicione o diretório 'C:\\ffmpeg\\ffmpeg-7.1.1\\bin' ao PATH do sistema.")
        print("Após adicionar o diretório ao PATH, reinicie o terminal.")

        # Lista de vídeos para transcrição
        videos = [
            "Reunião em _General_-20250224_190710-Gravação de Reunião.mp4",
            "Reunião em _General_-20250310_190641-Gravação de Reunião.mp4",
            "Reunião em _General_-20250317_191123-Gravação de Reunião.mp4",
        ]

        for video in videos:
            output_file = video.replace(".mp4", "_abnt.txt")
            transcribe_video(video, output_file)

        print("Transcrição completa de todos os vídeos.")
    else:
        print("A extração do ffmpeg falhou. A transcrição não pode continuar.")