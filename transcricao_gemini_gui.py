# -*- coding: utf-8 -*-
"""
Sistema de Transcrição com API Gemini - Interface Gráfica
Transcreve vídeos/áudios para texto com formatação ABNT
Exporta em múltiplos formatos: TXT, Markdown, PDF, DOCX
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
from datetime import datetime
import json
from pathlib import Path

class TranscricaoGeminiGUI:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.arquivos_selecionados = []
        self.configuracoes = self.carregar_configuracoes()

    def setup_window(self):
        """Configura a janela principal"""
        self.root.title("🎯 Transcrição Gemini ABNT - Sistema Profissional")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)

        # Configurar estilo
        style = ttk.Style()
        style.theme_use('clam')

        # Cores personalizadas
        self.cores = {
            'primaria': '#2E86AB',
            'secundaria': '#A23B72',
            'sucesso': '#F18F01',
            'fundo': '#F5F5F5',
            'texto': '#2C3E50'
        }

    def setup_variables(self):
        """Configura as variáveis do tkinter"""
        self.var_api_key = tk.StringVar()
        self.var_pasta_saida = tk.StringVar(value=os.getcwd())
        self.var_titulo = tk.StringVar(value="TRANSCRIÇÃO DE ÁUDIO/VÍDEO")
        self.var_autor = tk.StringVar()
        self.var_instituicao = tk.StringVar()
        self.var_formato_txt = tk.BooleanVar(value=True)
        self.var_formato_md = tk.BooleanVar(value=True)
        self.var_formato_pdf = tk.BooleanVar(value=False)
        self.var_formato_docx = tk.BooleanVar(value=False)
        self.var_progresso = tk.StringVar(value="Pronto para iniciar")

    def create_widgets(self):
        """Cria todos os widgets da interface"""
        # Frame principal com scroll
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Título principal
        titulo_frame = ttk.Frame(main_frame)
        titulo_frame.pack(fill=tk.X, pady=(0, 20))

        titulo_label = ttk.Label(
            titulo_frame,
            text="🎯 Sistema de Transcrição Gemini ABNT",
            font=('Arial', 18, 'bold')
        )
        titulo_label.pack()

        subtitulo_label = ttk.Label(
            titulo_frame,
            text="Transcreva vídeos e áudios com formatação ABNT profissional",
            font=('Arial', 10)
        )
        subtitulo_label.pack()

        # Notebook para abas
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Aba 1: Configuração
        self.create_config_tab()

        # Aba 2: Arquivos
        self.create_files_tab()

        # Aba 3: Processamento
        self.create_process_tab()

        # Aba 4: Resultados
        self.create_results_tab()

    def create_config_tab(self):
        """Cria a aba de configuração"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="⚙️ Configuração")

        # API Key
        api_frame = ttk.LabelFrame(config_frame, text="🔑 Configuração da API Gemini", padding=15)
        api_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(api_frame, text="Chave da API Gemini:").pack(anchor=tk.W)
        api_entry = ttk.Entry(api_frame, textvariable=self.var_api_key, show="*", width=60)
        api_entry.pack(fill=tk.X, pady=(5, 10))

        ttk.Button(
            api_frame,
            text="💾 Salvar API Key",
            command=self.salvar_api_key
        ).pack(anchor=tk.W)

        # Metadados do documento
        meta_frame = ttk.LabelFrame(config_frame, text="📄 Metadados do Documento", padding=15)
        meta_frame.pack(fill=tk.X, padx=10, pady=10)

        # Grid para metadados
        ttk.Label(meta_frame, text="Título:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(meta_frame, textvariable=self.var_titulo, width=50).grid(row=0, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)

        ttk.Label(meta_frame, text="Autor:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(meta_frame, textvariable=self.var_autor, width=50).grid(row=1, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)

        ttk.Label(meta_frame, text="Instituição:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(meta_frame, textvariable=self.var_instituicao, width=50).grid(row=2, column=1, sticky=tk.W+tk.E, padx=(10, 0), pady=5)

        meta_frame.columnconfigure(1, weight=1)

        # Pasta de saída
        pasta_frame = ttk.LabelFrame(config_frame, text="📁 Pasta de Saída", padding=15)
        pasta_frame.pack(fill=tk.X, padx=10, pady=10)

        pasta_inner = ttk.Frame(pasta_frame)
        pasta_inner.pack(fill=tk.X)

        ttk.Entry(pasta_inner, textvariable=self.var_pasta_saida, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(pasta_inner, text="📂 Escolher", command=self.escolher_pasta_saida).pack(side=tk.RIGHT, padx=(10, 0))

        # Formatos de exportação
        formato_frame = ttk.LabelFrame(config_frame, text="📤 Formatos de Exportação", padding=15)
        formato_frame.pack(fill=tk.X, padx=10, pady=10)

        formatos_inner = ttk.Frame(formato_frame)
        formatos_inner.pack(fill=tk.X)

        ttk.Checkbutton(formatos_inner, text="📄 TXT (ABNT)", variable=self.var_formato_txt).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(formatos_inner, text="📝 Markdown", variable=self.var_formato_md).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(formatos_inner, text="📕 PDF", variable=self.var_formato_pdf).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Checkbutton(formatos_inner, text="📘 DOCX", variable=self.var_formato_docx).pack(side=tk.LEFT)

    def create_files_tab(self):
        """Cria a aba de seleção de arquivos"""
        files_frame = ttk.Frame(self.notebook)
        self.notebook.add(files_frame, text="📁 Arquivos")

        # Botões de ação
        buttons_frame = ttk.Frame(files_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(
            buttons_frame,
            text="➕ Adicionar Arquivos",
            command=self.adicionar_arquivos
        ).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(
            buttons_frame,
            text="📁 Adicionar Pasta",
            command=self.adicionar_pasta
        ).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(
            buttons_frame,
            text="🗑️ Limpar Lista",
            command=self.limpar_arquivos
        ).pack(side=tk.LEFT, padx=(0, 10))

        # Lista de arquivos
        list_frame = ttk.LabelFrame(files_frame, text="📋 Arquivos Selecionados", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Treeview para lista de arquivos
        columns = ('Nome', 'Tipo', 'Tamanho', 'Status')
        self.tree_arquivos = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # Configurar colunas
        self.tree_arquivos.heading('Nome', text='Nome do Arquivo')
        self.tree_arquivos.heading('Tipo', text='Tipo')
        self.tree_arquivos.heading('Tamanho', text='Tamanho')
        self.tree_arquivos.heading('Status', text='Status')

        self.tree_arquivos.column('Nome', width=400)
        self.tree_arquivos.column('Tipo', width=100)
        self.tree_arquivos.column('Tamanho', width=100)
        self.tree_arquivos.column('Status', width=150)

        # Scrollbar para a lista
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree_arquivos.yview)
        self.tree_arquivos.configure(yscrollcommand=scrollbar.set)

        self.tree_arquivos.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_process_tab(self):
        """Cria a aba de processamento"""
        process_frame = ttk.Frame(self.notebook)
        self.notebook.add(process_frame, text="⚡ Processamento")

        # Status e controles
        status_frame = ttk.LabelFrame(process_frame, text="📊 Status do Processamento", padding=15)
        status_frame.pack(fill=tk.X, padx=10, pady=10)

        # Barra de progresso
        self.progress_bar = ttk.Progressbar(status_frame, mode='determinate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        # Label de status
        self.status_label = ttk.Label(status_frame, textvariable=self.var_progresso)
        self.status_label.pack()

        # Botões de controle
        control_frame = ttk.Frame(status_frame)
        control_frame.pack(fill=tk.X, pady=(10, 0))

        self.btn_iniciar = ttk.Button(
            control_frame,
            text="🚀 Iniciar Transcrição",
            command=self.iniciar_transcricao,
            style='Accent.TButton'
        )
        self.btn_iniciar.pack(side=tk.LEFT, padx=(0, 10))

        self.btn_parar = ttk.Button(
            control_frame,
            text="⏹️ Parar",
            command=self.parar_transcricao,
            state=tk.DISABLED
        )
        self.btn_parar.pack(side=tk.LEFT)

        # Log de processamento
        log_frame = ttk.LabelFrame(process_frame, text="📝 Log de Processamento", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_results_tab(self):
        """Cria a aba de resultados"""
        results_frame = ttk.Frame(self.notebook)
        self.notebook.add(results_frame, text="📊 Resultados")

        # Estatísticas
        stats_frame = ttk.LabelFrame(results_frame, text="📈 Estatísticas", padding=15)
        stats_frame.pack(fill=tk.X, padx=10, pady=10)

        self.stats_text = tk.Text(stats_frame, height=8, wrap=tk.WORD)
        self.stats_text.pack(fill=tk.X)

        # Arquivos gerados
        output_frame = ttk.LabelFrame(results_frame, text="📁 Arquivos Gerados", padding=10)
        output_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Treeview para arquivos gerados
        output_columns = ('Arquivo', 'Formato', 'Tamanho', 'Data')
        self.tree_resultados = ttk.Treeview(output_frame, columns=output_columns, show='headings')

        for col in output_columns:
            self.tree_resultados.heading(col, text=col)
            self.tree_resultados.column(col, width=200)

        self.tree_resultados.pack(fill=tk.BOTH, expand=True)

        # Botões de ação nos resultados
        result_buttons = ttk.Frame(output_frame)
        result_buttons.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(
            result_buttons,
            text="📂 Abrir Pasta",
            command=self.abrir_pasta_resultados
        ).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(
            result_buttons,
            text="👁️ Visualizar",
            command=self.visualizar_resultado
        ).pack(side=tk.LEFT)

    # Métodos de funcionalidade (continuação no próximo arquivo)
    def adicionar_arquivos(self):
        """Adiciona arquivos individuais"""
        tipos = [
            ("Arquivos de Mídia", "*.mp4 *.mp3 *.wav *.avi *.mov *.m4a *.flac"),
            ("Vídeos", "*.mp4 *.avi *.mov *.mkv"),
            ("Áudios", "*.mp3 *.wav *.m4a *.flac *.aac"),
            ("Todos os arquivos", "*.*")
        ]

        arquivos = filedialog.askopenfilenames(
            title="Selecionar arquivos para transcrição",
            filetypes=tipos
        )

        for arquivo in arquivos:
            self.adicionar_arquivo_lista(arquivo)

    def adicionar_pasta(self):
        """Adiciona todos os arquivos de mídia de uma pasta"""
        pasta = filedialog.askdirectory(title="Selecionar pasta com arquivos de mídia")
        if pasta:
            extensoes = {'.mp4', '.mp3', '.wav', '.avi', '.mov', '.m4a', '.flac', '.aac', '.mkv'}
            for arquivo in Path(pasta).rglob('*'):
                if arquivo.suffix.lower() in extensoes:
                    self.adicionar_arquivo_lista(str(arquivo))

    def adicionar_arquivo_lista(self, caminho_arquivo):
        """Adiciona um arquivo à lista"""
        if caminho_arquivo not in self.arquivos_selecionados:
            self.arquivos_selecionados.append(caminho_arquivo)

            # Obter informações do arquivo
            arquivo = Path(caminho_arquivo)
            nome = arquivo.name
            tipo = arquivo.suffix.upper()[1:]
            tamanho = self.formatar_tamanho(arquivo.stat().st_size)

            # Adicionar à treeview
            self.tree_arquivos.insert('', tk.END, values=(nome, tipo, tamanho, 'Aguardando'))

    def formatar_tamanho(self, bytes):
        """Formata o tamanho do arquivo"""
        for unidade in ['B', 'KB', 'MB', 'GB']:
            if bytes < 1024.0:
                return f"{bytes:.1f} {unidade}"
            bytes /= 1024.0
        return f"{bytes:.1f} TB"

    def limpar_arquivos(self):
        """Limpa a lista de arquivos"""
        self.arquivos_selecionados.clear()
        for item in self.tree_arquivos.get_children():
            self.tree_arquivos.delete(item)

    def escolher_pasta_saida(self):
        """Escolhe a pasta de saída"""
        pasta = filedialog.askdirectory(title="Escolher pasta de saída")
        if pasta:
            self.var_pasta_saida.set(pasta)

    def salvar_api_key(self):
        """Salva a API key"""
        if self.var_api_key.get().strip():
            self.salvar_configuracoes()
            messagebox.showinfo("Sucesso", "API Key salva com sucesso!")
        else:
            messagebox.showwarning("Aviso", "Por favor, insira uma API Key válida.")

    def carregar_configuracoes(self):
        """Carrega configurações salvas"""
        try:
            with open('config_gemini.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.var_api_key.set(config.get('api_key', ''))
                return config
        except FileNotFoundError:
            return {}

    def salvar_configuracoes(self):
        """Salva configurações"""
        config = {
            'api_key': self.var_api_key.get(),
            'autor': self.var_autor.get(),
            'instituicao': self.var_instituicao.get()
        }
        with open('config_gemini.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

    def log_message(self, message):
        """Adiciona mensagem ao log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()

    def iniciar_transcricao(self):
        """Inicia o processo de transcrição"""
        if not self.arquivos_selecionados:
            messagebox.showwarning("Aviso", "Selecione pelo menos um arquivo para transcrever.")
            return

        if not self.var_api_key.get().strip():
            messagebox.showwarning("Aviso", "Configure a API Key do Gemini primeiro.")
            return

        # Desabilitar botão e habilitar parar
        self.btn_iniciar.config(state=tk.DISABLED)
        self.btn_parar.config(state=tk.NORMAL)

        # Iniciar processamento em thread separada
        thread = threading.Thread(target=self.processar_arquivos)
        thread.daemon = True
        thread.start()

    def parar_transcricao(self):
        """Para o processo de transcrição"""
        self.btn_iniciar.config(state=tk.NORMAL)
        self.btn_parar.config(state=tk.DISABLED)
        self.log_message("❌ Processamento interrompido pelo usuário")

    def processar_arquivos(self):
        """Processa todos os arquivos selecionados"""
        try:
            from gemini_transcriber import GeminiTranscriber
            from abnt_formatter import ABNTFormatter

            # Verificar API key
            api_key = self.var_api_key.get().strip()
            if not api_key:
                self.log_message("❌ Erro: API Key do Gemini não configurada")
                return

            # Inicializar transcriber e formatter
            transcriber = GeminiTranscriber(api_key)
            formatter = ABNTFormatter()

            total_arquivos = len(self.arquivos_selecionados)
            self.progress_bar.config(maximum=total_arquivos)

            sucessos = 0
            falhas = 0

            for i, arquivo in enumerate(self.arquivos_selecionados):
                nome_arquivo = Path(arquivo).name
                self.var_progresso.set(f"Processando {i+1}/{total_arquivos}: {nome_arquivo}")
                self.log_message(f"🎯 Iniciando transcrição: {nome_arquivo}")

                try:
                    # Callback para progresso da transcrição
                    def callback_progresso(msg):
                        self.log_message(f"   {msg}")

                    # Transcrever arquivo
                    resultado = transcriber.transcrever_arquivo(arquivo, callback_progresso)

                    if resultado and resultado['texto']:
                        # Melhorar texto com IA
                        texto_melhorado = transcriber.melhorar_texto_com_gemini(
                            resultado['texto'], callback_progresso
                        )

                        # Gerar resumo e palavras-chave
                        resumo = transcriber.gerar_resumo(texto_melhorado, callback_progresso)
                        palavras_chave = transcriber.extrair_palavras_chave(texto_melhorado, callback_progresso)

                        # Configurar metadados
                        metadados = {
                            'titulo': self.var_titulo.get() or f"TRANSCRIÇÃO - {nome_arquivo}",
                            'autor': self.var_autor.get(),
                            'instituicao': self.var_instituicao.get(),
                            'arquivo_original': nome_arquivo,
                            'data_transcricao': resultado['data_transcricao'],
                            'palavras_estimadas': resultado['palavras_estimadas'],
                            'chunks_processados': resultado['chunks_processados'],
                            'resumo': resumo,
                            'palavras_chave': palavras_chave
                        }

                        # Gerar arquivos nos formatos selecionados
                        base_nome = Path(arquivo).stem
                        pasta_saida = self.var_pasta_saida.get()

                        if self.var_formato_txt.get():
                            arquivo_txt = os.path.join(pasta_saida, f"{base_nome}_abnt.txt")
                            formatter.exportar_txt(texto_melhorado, metadados, arquivo_txt)
                            self.log_message(f"   📄 TXT gerado: {arquivo_txt}")

                        if self.var_formato_md.get():
                            arquivo_md = os.path.join(pasta_saida, f"{base_nome}_abnt.md")
                            formatter.exportar_markdown(texto_melhorado, metadados, arquivo_md)
                            self.log_message(f"   📝 Markdown gerado: {arquivo_md}")

                        if self.var_formato_pdf.get():
                            arquivo_pdf = os.path.join(pasta_saida, f"{base_nome}_abnt.pdf")
                            formatter.exportar_pdf(texto_melhorado, metadados, arquivo_pdf)
                            self.log_message(f"   📕 PDF gerado: {arquivo_pdf}")

                        if self.var_formato_docx.get():
                            arquivo_docx = os.path.join(pasta_saida, f"{base_nome}_abnt.docx")
                            formatter.exportar_docx(texto_melhorado, metadados, arquivo_docx)
                            self.log_message(f"   📘 DOCX gerado: {arquivo_docx}")

                        sucessos += 1
                        self.log_message(f"✅ Concluído: {nome_arquivo}")

                    else:
                        falhas += 1
                        self.log_message(f"❌ Falha na transcrição: {nome_arquivo}")

                except Exception as e:
                    falhas += 1
                    self.log_message(f"❌ Erro ao processar {nome_arquivo}: {str(e)}")

                self.progress_bar.config(value=i+1)

            # Resumo final
            self.var_progresso.set("✅ Processamento concluído!")
            self.log_message(f"\n📊 RESUMO FINAL:")
            self.log_message(f"   ✅ Sucessos: {sucessos}")
            self.log_message(f"   ❌ Falhas: {falhas}")
            self.log_message(f"   📁 Arquivos salvos em: {self.var_pasta_saida.get()}")

            # Atualizar estatísticas
            self.atualizar_estatisticas(sucessos, falhas, total_arquivos)

        except ImportError as e:
            self.log_message(f"❌ Erro de importação: {e}")
            self.log_message("   Verifique se todos os módulos estão instalados")
        except Exception as e:
            self.log_message(f"❌ Erro inesperado: {e}")
        finally:
            self.btn_iniciar.config(state=tk.NORMAL)
            self.btn_parar.config(state=tk.DISABLED)

    def atualizar_estatisticas(self, sucessos, falhas, total):
        """Atualiza as estatísticas na aba de resultados"""
        stats_text = f"""
📊 ESTATÍSTICAS DO PROCESSAMENTO

Total de arquivos processados: {total}
✅ Sucessos: {sucessos}
❌ Falhas: {falhas}
📈 Taxa de sucesso: {(sucessos/total*100):.1f}%

📁 Pasta de saída: {self.var_pasta_saida.get()}

🕒 Processamento concluído em: {datetime.now().strftime('%d/%m/%Y às %H:%M:%S')}
        """

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def abrir_pasta_resultados(self):
        """Abre a pasta de resultados"""
        os.startfile(self.var_pasta_saida.get())

    def visualizar_resultado(self):
        """Visualiza um resultado selecionado"""
        selection = self.tree_resultados.selection()
        if selection:
            messagebox.showinfo("Visualizar", "Funcionalidade de visualização será implementada")

if __name__ == "__main__":
    root = tk.Tk()
    app = TranscricaoGeminiGUI(root)
    root.mainloop()
