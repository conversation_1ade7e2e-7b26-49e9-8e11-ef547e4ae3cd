# -*- coding: utf-8 -*-
"""
Sistema Simplificado de Transcrição de Vídeo para Texto ABNT
Versão que funciona sem dependências complexas
"""

import os
import speech_recognition as sr
from datetime import datetime
import re

def format_abnt_simple(text, title="TRANSCRIÇÃO DE ÁUDIO", author="", institution=""):
    """
    Aplica formatação ABNT básica ao texto transcrito.
    """
    # Data atual para o documento
    current_date = datetime.now().strftime("%d de %B de %Y")
    
    # Limpar e formatar o texto
    cleaned_text = clean_text_simple(text)
    
    # Dividir em parágrafos
    paragraphs = create_paragraphs_simple(cleaned_text)
    
    # Criar documento formatado
    doc = []
    
    # Cabeçalho ABNT
    doc.append("=" * 80)
    doc.append(f"{title.upper()}")
    doc.append("=" * 80)
    doc.append("")
    
    if author:
        doc.append(f"Autor: {author}")
    if institution:
        doc.append(f"Instituição: {institution}")
    
    doc.append(f"Data: {current_date}")
    doc.append("")
    doc.append("-" * 80)
    doc.append("")
    
    # Corpo do documento
    doc.append("TRANSCRIÇÃO:")
    doc.append("")
    
    for paragraph in paragraphs:
        # Indentação de parágrafo (1,25 cm simulado com espaços)
        indented_paragraph = f"    {paragraph}"
        doc.append(indented_paragraph)
        doc.append("")  # Espaçamento entre parágrafos
    
    # Rodapé
    doc.append("-" * 80)
    doc.append(f"Documento gerado automaticamente em {current_date}")
    doc.append(f"Total de parágrafos: {len(paragraphs)}")
    doc.append("=" * 80)
    
    return '\n'.join(doc)

def clean_text_simple(text):
    """
    Limpa e melhora o texto transcrito.
    """
    # Remover espaços extras
    text = re.sub(r'\s+', ' ', text)
    
    # Corrigir pontuação básica
    text = re.sub(r'\s+([,.!?;:])', r'\1', text)
    text = re.sub(r'([,.!?;:])\s*', r'\1 ', text)
    
    # Capitalizar início de frases
    sentences = re.split(r'([.!?]+)', text)
    cleaned_sentences = []
    
    for i, sentence in enumerate(sentences):
        if i % 2 == 0 and sentence.strip():  # Texto da frase
            sentence = sentence.strip()
            if sentence:
                sentence = sentence[0].upper() + sentence[1:].lower()
                cleaned_sentences.append(sentence)
        elif sentence.strip():  # Pontuação
            cleaned_sentences.append(sentence)
    
    return ''.join(cleaned_sentences)

def create_paragraphs_simple(text, max_words_per_paragraph=80):
    """
    Divide o texto em parágrafos seguindo padrões ABNT.
    """
    words = text.split()
    paragraphs = []
    current_paragraph = []
    
    for word in words:
        current_paragraph.append(word)
        
        # Criar novo parágrafo se atingir limite ou encontrar pausa natural
        if (len(current_paragraph) >= max_words_per_paragraph and 
            word.endswith(('.', '!', '?')) or
            len(current_paragraph) >= max_words_per_paragraph * 1.5):
            
            paragraphs.append(' '.join(current_paragraph))
            current_paragraph = []
    
    # Adicionar último parágrafo se houver
    if current_paragraph:
        paragraphs.append(' '.join(current_paragraph))
    
    return paragraphs

def transcribe_audio_file(audio_path):
    """
    Transcreve um arquivo de áudio usando SpeechRecognition.
    """
    try:
        r = sr.Recognizer()
        with sr.AudioFile(audio_path) as source:
            print("Carregando áudio...")
            audio = r.record(source)
            print("Transcrevendo áudio...")
            text = r.recognize_google(audio, language="pt-BR")
            return text
    except sr.UnknownValueError:
        print("SpeechRecognition não conseguiu entender o áudio")
        return None
    except sr.RequestError as e:
        print(f"Erro na API: {e}")
        return None
    except Exception as e:
        print(f"Erro na transcrição: {e}")
        return None

def demo_transcription():
    """
    Demonstração do sistema com texto de exemplo.
    """
    print("=" * 60)
    print("    DEMO - SISTEMA DE TRANSCRIÇÃO ABNT")
    print("=" * 60)
    print()
    
    # Texto de exemplo (simulando uma transcrição)
    sample_text = """
    Bom dia pessoal, hoje vamos falar sobre direito constitucional. 
    A constituição federal é a lei suprema do país. Ela estabelece 
    os direitos e deveres dos cidadãos. É importante estudar os 
    artigos da constituição para entender melhor nossos direitos. 
    A organização do estado brasileiro segue o modelo federativo. 
    Temos a união, os estados, o distrito federal e os municípios. 
    Cada ente federativo tem suas competências específicas. 
    Isso é fundamental para o funcionamento do sistema político brasileiro.
    """
    
    print("Texto de exemplo para demonstração:")
    print(sample_text.strip())
    print()
    
    # Configurar metadados
    title = input("Título do documento (Enter para padrão): ").strip()
    if not title:
        title = "TRANSCRIÇÃO DE AULA - DIREITO CONSTITUCIONAL"
    
    author = input("Autor (Enter para pular): ").strip()
    institution = input("Instituição (Enter para pular): ").strip()
    
    # Formatar texto
    formatted_text = format_abnt_simple(sample_text, title, author, institution)
    
    # Salvar arquivo
    output_file = "demo_transcricao_abnt.txt"
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(formatted_text)
    
    print(f"\n✓ Demonstração salva em: {output_file}")
    print("\nPrévia do documento formatado:")
    print("-" * 60)
    print(formatted_text[:500] + "..." if len(formatted_text) > 500 else formatted_text)

def transcribe_existing_audio():
    """
    Transcreve arquivos de áudio existentes na pasta.
    """
    print("\n--- TRANSCRIÇÃO DE ARQUIVO DE ÁUDIO ---")
    
    # Procurar arquivos de áudio
    audio_files = [f for f in os.listdir('.') if f.endswith(('.wav', '.mp3', '.flac'))]
    
    if not audio_files:
        print("Nenhum arquivo de áudio (.wav, .mp3, .flac) encontrado na pasta atual.")
        print("Para transcrever vídeos, você precisa primeiro extrair o áudio.")
        return
    
    print("\nArquivos de áudio disponíveis:")
    for i, audio in enumerate(audio_files, 1):
        print(f"{i}. {audio}")
    
    try:
        choice = int(input(f"\nEscolha um arquivo (1-{len(audio_files)}): ")) - 1
        if 0 <= choice < len(audio_files):
            audio_path = audio_files[choice]
            
            print(f"\nTranscrevendo: {audio_path}")
            text = transcribe_audio_file(audio_path)
            
            if text:
                print("Transcrição bem-sucedida!")
                
                # Configurar metadados
                title = input("Título do documento (opcional): ").strip()
                author = input("Autor (opcional): ").strip()
                institution = input("Instituição (opcional): ").strip()
                
                # Formatar e salvar
                formatted_text = format_abnt_simple(text, title, author, institution)
                output_file = audio_path.replace(audio_path.split('.')[-1], 'txt')
                
                with open(output_file, "w", encoding="utf-8") as f:
                    f.write(formatted_text)
                
                print(f"✓ Transcrição salva em: {output_file}")
            else:
                print("✗ Falha na transcrição.")
        else:
            print("Escolha inválida!")
    except ValueError:
        print("Por favor, digite um número válido.")

def main_menu():
    """
    Menu principal do sistema simplificado.
    """
    print("=" * 60)
    print("    SISTEMA SIMPLIFICADO DE TRANSCRIÇÃO ABNT")
    print("=" * 60)
    print()
    print("NOTA: Esta versão simplificada funciona com arquivos de áudio.")
    print("Para vídeos, primeiro extraia o áudio usando ferramentas externas.")
    print()
    
    while True:
        print("\nOpções disponíveis:")
        print("1. Demonstração com texto de exemplo")
        print("2. Transcrever arquivo de áudio existente")
        print("3. Instruções para extrair áudio de vídeo")
        print("4. Sair")
        print()
        
        choice = input("Escolha uma opção (1-4): ").strip()
        
        if choice == "1":
            demo_transcription()
        elif choice == "2":
            transcribe_existing_audio()
        elif choice == "3":
            show_audio_extraction_instructions()
        elif choice == "4":
            print("Saindo do sistema...")
            break
        else:
            print("Opção inválida! Tente novamente.")

def show_audio_extraction_instructions():
    """
    Mostra instruções para extrair áudio de vídeo.
    """
    print("\n--- INSTRUÇÕES PARA EXTRAIR ÁUDIO DE VÍDEO ---")
    print()
    print("Para usar este sistema com vídeos, primeiro extraia o áudio:")
    print()
    print("OPÇÃO 1 - Usando FFmpeg (linha de comando):")
    print("  ffmpeg -i seu_video.mp4 -vn -acodec pcm_s16le audio_extraido.wav")
    print()
    print("OPÇÃO 2 - Usando VLC Media Player:")
    print("  1. Abra o VLC")
    print("  2. Vá em Mídia > Converter/Salvar")
    print("  3. Adicione seu vídeo")
    print("  4. Escolha 'Converter'")
    print("  5. Selecione perfil de áudio (WAV)")
    print("  6. Salve o arquivo")
    print()
    print("OPÇÃO 3 - Usando ferramentas online:")
    print("  - CloudConvert")
    print("  - Online-Convert")
    print("  - Convertio")
    print()
    print("Depois de extrair o áudio, use a opção 2 do menu principal.")

if __name__ == "__main__":
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\nOperação interrompida pelo usuário.")
    except Exception as e:
        print(f"\nErro inesperado: {e}")
        print("Por favor, verifique os arquivos e tente novamente.")
